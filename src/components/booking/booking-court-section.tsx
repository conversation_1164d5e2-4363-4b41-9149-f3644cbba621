"use client";
import { useBookingStore } from "@/store/booking-store";
import React from "react";

const BookingCourtSection = () => {
  const { selectedSport, setSelectedSport } = useBookingStore();

  return (
    <div className="flex w-full flex-col items-center">
      <div className="w-full flex-col items-center justify-start">
        <h1 className="font-helvetica justify-center text-center text-4xl font-bold text-[#1c5534]">
          Booking Options
        </h1>
        <p className="font-helvetica justify-center self-stretch text-center font-normal text-[#364153]">
          Choose how you play. Book the experience that fits you.
        </p>
      </div>

      <div
        data-property-1="Default"
        className="container flex w-full items-center justify-start gap-10 py-6"
      >
        <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] bg-[url('/imgs/booking/padel.png')] p-2.5">
          <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] bg-[#1c5534]/20 p-2.5">
            <div className="justify-center text-center text-[25px] leading-[25px] font-bold tracking-tight text-white">
              Padel
            </div>
          </div>
        </div>
        <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] bg-[url('/imgs/booking/pickleball.png')] p-2.5 opacity-10">
          <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] p-2.5 bg-blend-luminosity">
            <div className="justify-center text-center">
              <p className="justify-center text-center text-[25px] leading-[25px] font-bold tracking-tight text-[#9f9f9f]">
                Pickleball
              </p>
              <p className="text-[15px] leading-[25px] font-normal tracking-tight text-[#9f9f9f]">
                Coming Soon
              </p>
            </div>
          </div>
        </div>
        <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] bg-[url('/imgs/booking/tennis.png')] p-2.5">
          <div className="flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] p-2.5 bg-blend-luminosity">
            <div className="justify-center text-center">
              <p className="text-[25px] leading-[25px] font-bold tracking-tight text-[#9f9f9f]">
                Tennis
              </p>
              <p className="text-[15px] leading-[25px] font-normal tracking-tight text-[#9f9f9f]">
                Coming Soon
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
