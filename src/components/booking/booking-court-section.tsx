"use client";
import { useBookingStore } from "@/store/booking-store";
import React, { useCallback } from "react";

type SportType = "Padel" | "Pickleball" | "Tennis";

const BookingCourtSection = () => {
  const { selectedSport, setSelectedSport } = useBookingStore();

  const handleSportSelect = useCallback(
    (sport: SportType) => {
      setSelectedSport(sport);
    },
    [setSelectedSport]
  );

  const getSportItemClasses = useCallback(
    (sport: SportType, isComingSoon: boolean = false) => {
      const isSelected = selectedSport === sport;

      // Base classes for the outer container
      const baseClasses =
        "flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] p-2.5 cursor-pointer transition-all duration-200 hover:scale-105";

      // Background image classes
      const bgClasses = {
        Padel: "bg-[url('/imgs/booking/padel.png')]",
        Pickleball: "bg-[url('/imgs/booking/pickleball.png')]",
        Tennis: "bg-[url('/imgs/booking/tennis.png')]",
      };

      // Opacity and blend mode for unselected items
      const stateClasses = isSelected
        ? ""
        : isComingSoon
          ? "opacity-10"
          : "opacity-60 hover:opacity-80";

      return `${baseClasses} ${bgClasses[sport]} ${stateClasses}`;
    },
    [selectedSport]
  );

  const getInnerOverlayClasses = useCallback(
    (sport: SportType) => {
      const isSelected = selectedSport === sport;
      const baseClasses =
        "flex h-[92px] flex-1 items-center justify-center gap-2.5 rounded-[10px] p-2.5";

      return isSelected ? `${baseClasses} bg-[#1c5534]/20` : `${baseClasses} bg-blend-luminosity`;
    },
    [selectedSport]
  );

  const getTextClasses = useCallback(
    (sport: SportType, isTitle: boolean = true) => {
      const isSelected = selectedSport === sport;
      const baseClasses = isTitle
        ? "text-[25px] leading-[25px] font-bold tracking-tight"
        : "text-[15px] leading-[25px] font-normal tracking-tight";

      const colorClasses = isSelected ? "text-white" : "text-[#9f9f9f]";

      return `${baseClasses} ${colorClasses} justify-center text-center`;
    },
    [selectedSport]
  );

  return (
    <div className="flex w-full flex-col items-center">
      <div className="w-full flex-col items-center justify-start">
        <h1 className="font-helvetica justify-center text-center text-4xl font-bold text-[#1c5534]">
          Booking Options
        </h1>
        <p className="font-helvetica justify-center self-stretch text-center font-normal text-[#364153]">
          Choose how you play. Book the experience that fits you.
        </p>
      </div>

      <div
        data-property-1="Default"
        className="container flex w-full items-center justify-start gap-10 py-6"
      >
        {/* Padel */}
        <div
          className={getSportItemClasses("Padel")}
          onClick={() => handleSportSelect("Padel")}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleSportSelect("Padel");
            }
          }}
        >
          <div className={getInnerOverlayClasses("Padel")}>
            <div className="justify-center text-center">
              <div className={getTextClasses("Padel")}>Padel</div>
            </div>
          </div>
        </div>

        {/* Pickleball */}
        <div
          className={getSportItemClasses("Pickleball", true)}
          onClick={() => handleSportSelect("Pickleball")}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleSportSelect("Pickleball");
            }
          }}
        >
          <div className={getInnerOverlayClasses("Pickleball")}>
            <div className="justify-center text-center">
              <p className={getTextClasses("Pickleball")}>Pickleball</p>
              <p className={getTextClasses("Pickleball", false)}>Coming Soon</p>
            </div>
          </div>
        </div>

        {/* Tennis */}
        <div
          className={getSportItemClasses("Tennis", true)}
          onClick={() => handleSportSelect("Tennis")}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleSportSelect("Tennis");
            }
          }}
        >
          <div className={getInnerOverlayClasses("Tennis")}>
            <div className="justify-center text-center">
              <p className={getTextClasses("Tennis")}>Tennis</p>
              <p className={getTextClasses("Tennis", false)}>Coming Soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
